export const global: any;
export const calledInOrder: typeof import("./called-in-order");
export const className: typeof import("./class-name");
export const deprecated: typeof import("./deprecated");
export const every: (obj: any, fn: Function) => boolean;
export const functionName: (func: Function) => string;
export const orderByFirstCall: typeof import("./order-by-first-call");
export const prototypes: {
    array: any;
    function: any;
    map: any;
    object: any;
    set: any;
    string: any;
};
export const typeOf: (value: any) => string;
export const valueToString: typeof import("./value-to-string");
