{"name": "@types/crypto-js", "version": "4.2.2", "description": "TypeScript definitions for crypto-js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/crypto-js", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "misak113", "url": "https://github.com/misak113"}, {"name": "<PERSON>", "githubUsername": "maxi<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/maximlysenko"}, {"name": "<PERSON>", "githubUsername": "mymindstorm", "url": "https://github.com/mymindstorm"}, {"name": "<PERSON><PERSON>", "githubUsername": "SevenOutman", "url": "https://github.com/SevenOutman"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/crypto-js"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "3f6bc86fd9a54e71d98b564ebd76c1c858f08d36ab34f8be2560e0f31e55c6a1", "typeScriptVersion": "4.6"}