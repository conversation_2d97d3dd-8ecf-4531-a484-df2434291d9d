import { Request, Response } from 'express';
import { Product } from '../models/Product';
import { BlockchainService } from '../services/BlockchainService';
import { Transaction } from '../blockchain/Transaction';

export class ProductController {
    constructor(private blockchainService: BlockchainService) {}

    public async createProduct(req: Request, res: Response): Promise<void> {
        try {
            const { name, batchNumber, productionDate, manufacturer, specifications } = req.body;
            
            const product = new Product(
                Date.now().toString(),
                name,
                batchNumber,
                new Date(productionDate),
                manufacturer,
                specifications
            );

            const transaction = new Transaction(
                product.id,
                'CREATE',
                manufacturer,
                'MANUFACTURER',
                product.toJSON()
            );

            const blockHash = await this.blockchainService.addTransaction(transaction);

            res.status(201).json({
                message: '产品已创建并添加到区块链',
                product,
                blockHash
            });
        } catch (error) {
            console.error('创建产品失败:', error);
            res.status(500).json({
                message: '创建产品失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }

    public async getProduct(req: Request, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const history = await this.blockchainService.getProductHistory(id);

            if (history.length > 0) {
                // 找到CREATE操作的交易来获取产品基本信息
                const createTransaction = history.find(tx => tx.operation === 'CREATE');

                if (createTransaction && createTransaction.details) {
                    // 返回产品详细信息
                    res.status(200).json({
                        id: id,
                        name: createTransaction.details.name || '未知产品',
                        batchNumber: createTransaction.details.batchNumber || '未知',
                        productionDate: createTransaction.details.productionDate || null,
                        manufacturer: createTransaction.details.manufacturer || '未知',
                        specifications: createTransaction.details.specifications || '无',
                        status: '正常',
                        createdAt: createTransaction.details.createdAt || createTransaction.timestamp
                    });
                } else {
                    // 如果没有CREATE交易，返回基本信息
                    res.status(200).json({
                        id: id,
                        name: '未知产品',
                        batchNumber: '未知',
                        productionDate: null,
                        manufacturer: '未知',
                        specifications: '无',
                        status: '数据不完整'
                    });
                }
            } else {
                // 没有找到任何记录
                res.status(404).json({
                    message: '未找到该产品的记录',
                    id: id
                });
            }
        } catch (error) {
            console.error('获取产品信息失败:', error);
            res.status(500).json({
                message: '获取产品信息失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }

    public async getAllProducts(req: Request, res: Response): Promise<void> {
        try {
            const products = await this.blockchainService.getAllProducts();
            res.status(200).json(products);
        } catch (error) {
            console.error('获取产品列表失败:', error);
            res.status(500).json({
                message: '获取产品列表失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
}