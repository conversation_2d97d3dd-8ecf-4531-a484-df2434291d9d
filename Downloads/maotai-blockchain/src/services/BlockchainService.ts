// src/services/BlockchainService.ts

import { Chain } from '../blockchain/Chain';
import { Block } from '../blockchain/Block';
import { Transaction } from '../blockchain/Transaction';

export class BlockchainService {
    private blockchain: Chain;

    constructor() {
        this.blockchain = new Chain();
    }

    public async addTransaction(transaction: Transaction): Promise<string> {
        const block = new Block(
            this.blockchain.lastBlock().index + 1,
            [transaction],
            this.blockchain.lastBlock().hash
        );
        this.blockchain.addBlock(block);
        return block.hash;
    }

    public async getProductHistory(productId: string): Promise<Transaction[]> {
        return this.blockchain.getTransactionsByProductId(productId);
    }

    public async getAllProducts(): Promise<any[]> {
        const products = new Map<string, any>();

        for (const block of this.blockchain.getChain()) {
            for (const transaction of block.transactions) {
                if (transaction.operation === 'CREATE' && transaction.details) {
                    // 确保产品信息包含所有必要字段
                    const productInfo = {
                        id: transaction.productId,
                        name: transaction.details.name || '未知产品',
                        batchNumber: transaction.details.batchNumber || '未知',
                        productionDate: transaction.details.productionDate || null,
                        manufacturer: transaction.details.manufacturer || '未知',
                        specifications: transaction.details.specifications || '无',
                        createdAt: transaction.details.createdAt || transaction.timestamp
                    };
                    products.set(transaction.productId, productInfo);
                }
            }
        }

        return Array.from(products.values());
    }

    public validateChain(): boolean {
        return this.blockchain.validateChain();
    }

    public getChain(): Block[] {
        return this.blockchain.getChain();
    }
}