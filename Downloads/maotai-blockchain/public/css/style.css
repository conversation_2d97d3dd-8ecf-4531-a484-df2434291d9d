body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    color: #333;
}

header {
    background-color: #1a1a1a;
    color: white;
    padding: 1rem;
    text-align: center;
}

main {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

section {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 5px;
}

h1, h2 {
    color: #1a1a1a;
}

form {
    display: flex;
    gap: 1rem;
}

input {
    padding: 0.5rem;
    flex-grow: 1;
}

button {
    padding: 0.5rem 1rem;
    background-color: #1a1a1a;
    color: white;
    border: none;
    cursor: pointer;
}

button:hover {
    background-color: #333;
}

#product-details, #blockchain-details {
    padding: 1rem;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.product-item {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-item:hover {
    background-color: #f0f0f0;
    border-color: #1a1a1a;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.block-record {
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 3px;
    padding: 0.8rem;
    margin-bottom: 0.5rem;
}

.block-record hr {
    border: none;
    border-top: 1px solid #eee;
    margin: 0.5rem 0;
}

#products-container {
    max-height: 400px;
    overflow-y: auto;
}