// 全局函数：搜索产品
function searchProduct(productId) {
    document.getElementById('product-id').value = productId;
    document.getElementById('search-form').dispatchEvent(new Event('submit'));
}

document.addEventListener('DOMContentLoaded', () => {
    const registerForm = document.getElementById('register-form');
    const searchForm = document.getElementById('search-form');
    const refreshBtn = document.getElementById('refresh-products');
    const productDetails = document.getElementById('product-details');
    const blockchainDetails = document.getElementById('blockchain-details');
    const productsContainer = document.getElementById('products-container');

    // 加载产品列表
    const loadProducts = async () => {
        try {
            const response = await fetch('/api/products');

            if (response.ok) {
                const products = await response.json();

                if (products && products.length > 0) {
                    productsContainer.innerHTML = products.map(product => `
                        <div class="product-item" onclick="searchProduct('${product.id}')">
                            <h3>${product.name || '未知产品'}</h3>
                            <p><strong>ID:</strong> ${product.id}</p>
                            <p><strong>批次:</strong> ${product.batchNumber || '未知'}</p>
                            <p><strong>生产日期:</strong> ${product.productionDate ? new Date(product.productionDate).toLocaleDateString() : '未知'}</p>
                            <p><strong>制造商:</strong> ${product.manufacturer || '未知'}</p>
                            <p style="font-size: 12px; color: #666;">点击查看详情</p>
                        </div>
                    `).join('');
                } else {
                    productsContainer.innerHTML = '<p>暂无产品记录</p>';
                }
            } else {
                productsContainer.innerHTML = '<p>加载产品列表失败</p>';
            }
        } catch (error) {
            console.error('加载产品列表失败:', error);
            productsContainer.innerHTML = '<p>加载产品列表失败</p>';
        }
    };

    // 初始化加载产品列表
    loadProducts();

    // 刷新按钮事件
    refreshBtn.addEventListener('click', loadProducts);

    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const productData = {
            name: document.getElementById('product-name').value,
            batchNumber: document.getElementById('batch-number').value,
            productionDate: document.getElementById('production-date').value,
            manufacturer: document.getElementById('manufacturer').value,
            specifications: document.getElementById('specifications').value
        };

        try {
            const response = await fetch('/api/products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(productData)
            });

            const result = await response.json();
            const productId = result.product.id;
            alert(`产品注册成功！产品ID: ${productId}\n请等待3秒后查询`);
            registerForm.reset();
            
            // 自动填充查询表单
            setTimeout(() => {
                document.getElementById('product-id').value = productId;
                searchForm.dispatchEvent(new Event('submit'));
            }, 3000);
        } catch (error) {
            console.error('注册失败:', error);
            alert('产品注册失败，请重试');
        }
    });

    searchForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const productId = document.getElementById('product-id').value;

        if (!productId.trim()) {
            alert('请输入产品ID');
            return;
        }

        try {
            // 获取产品信息
            const productRes = await fetch(`/api/products/${productId}`);

            if (productRes.ok) {
                const productData = await productRes.json();

                // 显示产品信息
                productDetails.innerHTML = `
                    <h3>${productData.name || '未知产品'}</h3>
                    <p><strong>产品ID:</strong> ${productData.id || productId}</p>
                    <p><strong>生产日期:</strong> ${productData.productionDate ? new Date(productData.productionDate).toLocaleDateString() : '未知'}</p>
                    <p><strong>批次号:</strong> ${productData.batchNumber || '未知'}</p>
                    <p><strong>制造商:</strong> ${productData.manufacturer || '未知'}</p>
                    <p><strong>规格:</strong> ${productData.specifications || '无'}</p>
                    <p><strong>当前状态:</strong> ${productData.status || '正常'}</p>
                `;
            } else {
                productDetails.innerHTML = `<p>未找到产品ID为 ${productId} 的产品信息</p>`;
            }

            // 获取区块链溯源信息
            const traceRes = await fetch(`/api/traces/${productId}`);

            if (traceRes.ok) {
                const traceData = await traceRes.json();

                // 显示区块链信息
                if (traceData.history && traceData.history.length > 0) {
                    blockchainDetails.innerHTML = `
                        <h4>溯源记录 (共${traceData.history.length}条)</h4>
                        ${traceData.history.map((record, index) => `
                            <div class="block-record">
                                <p><strong>记录 ${index + 1}:</strong></p>
                                <p><strong>操作类型:</strong> ${record.operation}</p>
                                <p><strong>时间戳:</strong> ${new Date(record.timestamp).toLocaleString()}</p>
                                <p><strong>位置:</strong> ${record.location || '未知'}</p>
                                <p><strong>操作员:</strong> ${record.operator || '未知'}</p>
                                <p><strong>详情:</strong> ${typeof record.details === 'object' ? JSON.stringify(record.details, null, 2) : record.details || '无'}</p>
                                <hr>
                            </div>
                        `).join('')}
                    `;
                } else {
                    blockchainDetails.innerHTML = '<p>暂无溯源记录</p>';
                }
            } else {
                blockchainDetails.innerHTML = '<p>获取溯源记录失败</p>';
            }

        } catch (error) {
            console.error('查询错误:', error);
            productDetails.innerHTML = '<p>查询失败，请稍后再试或检查产品ID</p>';
            blockchainDetails.innerHTML = '<p>区块链数据可能还在同步中...</p>';
        }
    });
});
