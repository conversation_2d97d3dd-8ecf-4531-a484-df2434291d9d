document.addEventListener('DOMContentLoaded', () => {
    const registerForm = document.getElementById('register-form');
    const searchForm = document.getElementById('search-form');
    const refreshBtn = document.getElementById('refresh-products');
    const productDetails = document.getElementById('product-details');
    const blockchainDetails = document.getElementById('blockchain-details');
    const productsContainer = document.getElementById('products-container');

    // 加载产品列表
    const loadProducts = async () => {
        try {
            const response = await fetch('/api/products');
            const products = await response.json();
            
            productsContainer.innerHTML = products.map(product => `
                <div class="product-item">
                    <h3>${product.name}</h3>
                    <p>ID: ${product.id}</p>
                    <p>批次: ${product.batchNumber}</p>
                    <p>生产日期: ${new Date(product.productionDate).toLocaleDateString()}</p>
                </div>
            `).join('');
        } catch (error) {
            console.error('加载产品列表失败:', error);
            productsContainer.innerHTML = '<p>加载产品列表失败</p>';
        }
    };

    // 初始化加载产品列表
    loadProducts();

    // 刷新按钮事件
    refreshBtn.addEventListener('click', loadProducts);

    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const productData = {
            name: document.getElementById('product-name').value,
            batchNumber: document.getElementById('batch-number').value,
            productionDate: document.getElementById('production-date').value,
            manufacturer: document.getElementById('manufacturer').value,
            specifications: document.getElementById('specifications').value
        };

        try {
            const response = await fetch('/api/products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(productData)
            });

            const result = await response.json();
            const productId = result.product.id;
            alert(`产品注册成功！产品ID: ${productId}\n请等待3秒后查询`);
            registerForm.reset();
            
            // 自动填充查询表单
            setTimeout(() => {
                document.getElementById('product-id').value = productId;
                searchForm.dispatchEvent(new Event('submit'));
            }, 3000);
        } catch (error) {
            console.error('注册失败:', error);
            alert('产品注册失败，请重试');
        }
    });

    searchForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const productId = document.getElementById('product-id').value;
        
        try {
            // 获取产品信息
            const productRes = await fetch(`/api/products/${productId}`);
            const productData = await productRes.json();
            
            // 获取区块链溯源信息
            const traceRes = await fetch(`/api/traces/${productId}`);
            const traceData = await traceRes.json();

            // 显示产品信息
            productDetails.innerHTML = `
                <h3>${productData.name}</h3>
                <p>生产日期: ${productData.productionDate ? new Date(productData.productionDate).toLocaleDateString() : '未知'}</p>
                <p>批次号: ${productData.batchNumber || '未知'}</p>
                <p>当前状态: ${productData.status || '正常'}</p>
            `;

            // 显示区块链信息
            if (traceData.history && traceData.history.length > 0) {
                blockchainDetails.innerHTML = traceData.history.map(record => `
                    <div class="block-record">
                        <p><strong>操作类型:</strong> ${record.operation}</p>
                        <p><strong>时间戳:</strong> ${new Date(record.timestamp).toLocaleString()}</p>
                        <p><strong>详情:</strong> ${record.details}</p>
                        <hr>
                    </div>
                `).join('');
            } else {
                blockchainDetails.innerHTML = '<p>暂无溯源记录</p>';
            }
            
        } catch (error) {
            console.error('Error:', error);
            productDetails.innerHTML = '<p>查询失败，请稍后再试或检查产品ID</p>';
            blockchainDetails.innerHTML = '<p>区块链数据可能还在同步中...</p>';
        }
    });
});
